package kr.wayplus.qr_hallimpark.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * API 키 인증 필터
 * - /api/assignments 경로에 대한 API 키 인증 처리
 * - WayQRConnect 키 검증
 */
@Slf4j
public class ApiKeyAuthenticationFilter extends OncePerRequestFilter {

    private static final String API_KEY_HEADER = "X-API-Key";
    private static final String VALID_API_KEY = "WayQRConnect";
    private static final String ASSIGNMENT_API_PATH = "/api/assignments";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        String method = request.getMethod();

        log.info("Request received: {} {}", method, requestURI);

        // /api/assignments 경로에 대해서만 API 키 검증 (보안 유지)
        if (requestURI.startsWith(ASSIGNMENT_API_PATH)) {
            log.info("API key authentication required for: {} {}", method, requestURI);

            String apiKey = request.getHeader(API_KEY_HEADER);
            log.info("Received API key: {}", apiKey != null ? "***" + apiKey.substring(Math.max(0, apiKey.length() - 4)) : "null");

            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.warn("Missing API key for request: {} {}", method, requestURI);
                sendErrorResponse(response, "API 키가 필요합니다.", "MISSING_API_KEY");
                return;
            }

            if (!VALID_API_KEY.equals(apiKey)) {
                log.warn("Invalid API key for request: {} {}. Expected: {}, Provided: {}", method, requestURI, VALID_API_KEY, apiKey);
                sendErrorResponse(response, "유효하지 않은 API 키입니다.", "INVALID_API_KEY");
                return;
            }

            log.info("API key authentication successful for: {} {}", method, requestURI);
        }

        // 인증 성공 또는 인증이 필요하지 않은 경우 다음 필터로 진행
        filterChain.doFilter(request, response);
    }

    /**
     * 인증 실패 시 에러 응답 전송
     * @param response HTTP 응답
     * @param message 에러 메시지
     * @param errorCode 에러 코드
     * @throws IOException IO 예외
     */
    private void sendErrorResponse(HttpServletResponse response, String message, String errorCode) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", message);
        errorResponse.put("errorCode", errorCode);

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush(); // 버퍼 플러시 추가
    }
}
