package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QR-문제 연결 해제 요청 모델
 * - API 서버에서 전송하는 연결 해제 요청 데이터
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentDeleteRequest {

    /**
     * QR 코드 고유 식별자
     */
    private String qrCodeId;

    /**
     * 문제 ID
     */
    private Long quizId;

    /**
     * 매핑 ID (직접 삭제하는 경우)
     * - qrCodeId, quizId 대신 mappingId로 직접 삭제 가능
     */
    private Long mappingId;

    /**
     * 삭제 사유 (선택사항)
     */
    private String deleteReason;

    /**
     * 요청 유효성 검증
     * @return 유효한 요청인지 여부
     */
    public boolean isValid() {
        // mappingId가 있거나, qrCodeId와 quizId가 모두 있어야 함
        return (mappingId != null) || 
               (qrCodeId != null && !qrCodeId.trim().isEmpty() && quizId != null);
    }

    /**
     * 삭제 방식 확인
     * @return true: mappingId로 삭제, false: qrCodeId+quizId로 삭제
     */
    public boolean isDirectMappingDelete() {
        return mappingId != null;
    }
}
